# Default values for abt-sbol-gate.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

application: abt-sbol-gate

replicaCount: 1

image:
  repository: abt-sbol-gate
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "dev"

imagePullSecrets:
  - name: "default-secret"
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: { }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: { }

podSecurityContext: { }
# fsGroup: 2000

securityContext: { }
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

service:
  http:
    type: ClusterIP
    port: 8080
    targetPort: 8080

resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

nodeSelector: { }

tolerations: [ ]

affinity: { }

env:
  client:
    logging:
      enable: true
  db:
    migration:
      enable: true
  keycloak:
    client_id: "test-auth"
    realm:
      url: "https://dev-auth.sbertroika.tech/realms/test-asop"

livenessProbe:
  enabled: false
  path: /actuator/health/liveness
  port: 8080
  initialDelaySeconds: 10
  periodSeconds: 30
  failureThreshold: 3
  successThreshold: 1
  timeoutSeconds: 1

readinessProbe:
  enabled: false
  path: /actuator/health/readiness
  port: 8080
  initialDelaySeconds: 60
  periodSeconds: 30
  failureThreshold: 3
  successThreshold: 1
  timeoutSeconds: 1