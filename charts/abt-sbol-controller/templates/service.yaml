apiVersion: v1
kind: Service
metadata:
  name: {{ include "abt-sbol-controller.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "abt-sbol-controller.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.http.type }}
  ports:
    - port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "abt-sbol-controller.selectorLabels" . | nindent 4 }} 