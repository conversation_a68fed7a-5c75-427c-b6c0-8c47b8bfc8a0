apiVersion: v1
kind: Service
metadata:
  name: {{ include "abt-controller.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "abt-controller.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.http.type }}
  ports:
    - port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "abt-controller.selectorLabels" . | nindent 4 }} 