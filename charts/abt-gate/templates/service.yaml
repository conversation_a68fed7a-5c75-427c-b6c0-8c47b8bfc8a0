apiVersion: v1
kind: Service
metadata:
  name: {{ include "abt-gate.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "abt-gate.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.grpc.type }}
  ports:
    - port: {{ .Values.service.grpc.port }}
      targetPort: {{ .Values.service.grpc.targetPort }}
      protocol: TCP
      name: grpc
    - port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "abt-gate.selectorLabels" . | nindent 4 }}